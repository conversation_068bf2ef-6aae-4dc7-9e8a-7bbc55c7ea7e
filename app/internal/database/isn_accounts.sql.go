// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: isn_accounts.sql

package database

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const CreateIsnAccount = `-- name: CreateIsnAccount :one
INSERT INTO isn_accounts (
    id,
    created_at,
    updated_at,
    isn_id,
    account_id,
    permission
) VALUES (gen_random_uuid(), now(), now(), $1, $2, $3)
RETURNING id, created_at, updated_at, isn_id, account_id, permission
`

type CreateIsnAccountParams struct {
	IsnID      uuid.UUID `json:"isn_id"`
	AccountID  uuid.UUID `json:"account_id"`
	Permission string    `json:"permission"`
}

func (q *Queries) CreateIsnAccount(ctx context.Context, arg CreateIsnAccountParams) (IsnAccount, error) {
	row := q.db.QueryRow(ctx, CreateIsnAccount, arg.IsnID, arg.AccountID, arg.Permission)
	var i IsnAccount
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsnID,
		&i.AccountID,
		&i.Permission,
	)
	return i, err
}

const DeleteIsnAccount = `-- name: DeleteIsnAccount :execrows
DELETE FROM isn_accounts
WHERE isn_id =  $1
AND account_id = $2
`

type DeleteIsnAccountParams struct {
	IsnID     uuid.UUID `json:"isn_id"`
	AccountID uuid.UUID `json:"account_id"`
}

func (q *Queries) DeleteIsnAccount(ctx context.Context, arg DeleteIsnAccountParams) (int64, error) {
	result, err := q.db.Exec(ctx, DeleteIsnAccount, arg.IsnID, arg.AccountID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const GetAccountsByIsnID = `-- name: GetAccountsByIsnID :many
SELECT
    ia.id,
    ia.created_at,
    ia.updated_at,
    ia.isn_id,
    ia.account_id,
    ia.permission,
    a.account_type,
    a.is_active,
    COALESCE(u.email, sa.client_contact_email) AS email,
    COALESCE(u.user_role, 'member') AS account_role,
    sa.client_id,
    sa.client_organization
FROM isn_accounts ia
JOIN accounts a ON a.id = ia.account_id
LEFT OUTER JOIN users u ON u.account_id = ia.account_id
LEFT OUTER JOIN service_accounts sa ON sa.account_id = ia.account_id
WHERE ia.isn_id = $1
ORDER BY a.account_type, COALESCE(u.email, sa.client_contact_email)
`

type GetAccountsByIsnIDRow struct {
	ID                 uuid.UUID `json:"id"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	IsnID              uuid.UUID `json:"isn_id"`
	AccountID          uuid.UUID `json:"account_id"`
	Permission         string    `json:"permission"`
	AccountType        string    `json:"account_type"`
	IsActive           bool      `json:"is_active"`
	Email              string    `json:"email"`
	AccountRole        string    `json:"account_role"`
	ClientID           *string   `json:"client_id"`
	ClientOrganization *string   `json:"client_organization"`
}

// get all accounts that have access to a specific ISN
func (q *Queries) GetAccountsByIsnID(ctx context.Context, isnID uuid.UUID) ([]GetAccountsByIsnIDRow, error) {
	rows, err := q.db.Query(ctx, GetAccountsByIsnID, isnID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAccountsByIsnIDRow
	for rows.Next() {
		var i GetAccountsByIsnIDRow
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsnID,
			&i.AccountID,
			&i.Permission,
			&i.AccountType,
			&i.IsActive,
			&i.Email,
			&i.AccountRole,
			&i.ClientID,
			&i.ClientOrganization,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetIsnAccountByIsnAndAccountID = `-- name: GetIsnAccountByIsnAndAccountID :one
SELECT ia.id, ia.created_at, ia.updated_at, ia.isn_id, ia.account_id, ia.permission, i.slug as isn_slug FROM isn_accounts ia
JOIN isn i 
ON i.id = ia.isn_id
WHERE ia.isn_id = $1 
AND ia.account_id = $2
`

type GetIsnAccountByIsnAndAccountIDParams struct {
	IsnID     uuid.UUID `json:"isn_id"`
	AccountID uuid.UUID `json:"account_id"`
}

type GetIsnAccountByIsnAndAccountIDRow struct {
	ID         uuid.UUID `json:"id"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	IsnID      uuid.UUID `json:"isn_id"`
	AccountID  uuid.UUID `json:"account_id"`
	Permission string    `json:"permission"`
	IsnSlug    string    `json:"isn_slug"`
}

func (q *Queries) GetIsnAccountByIsnAndAccountID(ctx context.Context, arg GetIsnAccountByIsnAndAccountIDParams) (GetIsnAccountByIsnAndAccountIDRow, error) {
	row := q.db.QueryRow(ctx, GetIsnAccountByIsnAndAccountID, arg.IsnID, arg.AccountID)
	var i GetIsnAccountByIsnAndAccountIDRow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsnID,
		&i.AccountID,
		&i.Permission,
		&i.IsnSlug,
	)
	return i, err
}

const GetIsnAccountsByAccountID = `-- name: GetIsnAccountsByAccountID :many
SELECT ia.id, ia.created_at, ia.updated_at, ia.isn_id, ia.account_id, ia.permission, i.slug as isn_slug FROM isn_accounts ia
JOIN isn i
ON i.id = ia.isn_id
WHERE ia.account_id = $1
`

type GetIsnAccountsByAccountIDRow struct {
	ID         uuid.UUID `json:"id"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	IsnID      uuid.UUID `json:"isn_id"`
	AccountID  uuid.UUID `json:"account_id"`
	Permission string    `json:"permission"`
	IsnSlug    string    `json:"isn_slug"`
}

// get all the isns an account has access to.
func (q *Queries) GetIsnAccountsByAccountID(ctx context.Context, accountID uuid.UUID) ([]GetIsnAccountsByAccountIDRow, error) {
	rows, err := q.db.Query(ctx, GetIsnAccountsByAccountID, accountID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetIsnAccountsByAccountIDRow
	for rows.Next() {
		var i GetIsnAccountsByAccountIDRow
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsnID,
			&i.AccountID,
			&i.Permission,
			&i.IsnSlug,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateIsnAccount = `-- name: UpdateIsnAccount :one
UPDATE isn_accounts SET 
    updated_at = now(),
    permission = $3
WHERE isn_id =  $1
AND account_id = $2
RETURNING id, created_at, updated_at, isn_id, account_id, permission
`

type UpdateIsnAccountParams struct {
	IsnID      uuid.UUID `json:"isn_id"`
	AccountID  uuid.UUID `json:"account_id"`
	Permission string    `json:"permission"`
}

func (q *Queries) UpdateIsnAccount(ctx context.Context, arg UpdateIsnAccountParams) (IsnAccount, error) {
	row := q.db.QueryRow(ctx, UpdateIsnAccount, arg.IsnID, arg.AccountID, arg.Permission)
	var i IsnAccount
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsnID,
		&i.AccountID,
		&i.Permission,
	)
	return i, err
}

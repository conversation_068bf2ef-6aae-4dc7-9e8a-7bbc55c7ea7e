// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: signal_batches.sql

package database

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const CloseISNSignalBatchByAccountID = `-- name: CloseISNSignalBatchByAccountID :execrows
UPDATE signal_batches 
SET is_latest = FALSE
WHERE isn_id = $1 and account_id = $2
`

type CloseISNSignalBatchByAccountIDParams struct {
	IsnID     uuid.UUID `json:"isn_id"`
	AccountID uuid.UUID `json:"account_id"`
}

func (q *Queries) CloseISNSignalBatchByAccountID(ctx context.Context, arg CloseISNSignalBatchByAccountIDParams) (int64, error) {
	result, err := q.db.Exec(ctx, CloseISNSignalBatchByAccountID, arg.IsnID, arg.AccountID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const CreateOrGetWebUserSignalBatch = `-- name: CreateOrGetWebUserSignalBatch :one
WITH isn_record AS (
    SELECT id
    FROM isn
    WHERE isn.slug = $1
),
inserted AS (
    INSERT INTO signal_batches (
        id,
        created_at,
        updated_at,
        isn_id,
        account_id,
        is_latest
    )
    SELECT
        gen_random_uuid(),
        now(),
        now(),
        isn_record.id,
        $2, -- account_id
        TRUE
    FROM isn_record
    ON CONFLICT (account_id, isn_id) WHERE is_latest = TRUE
    DO NOTHING
    RETURNING id
)
SELECT id as batch_id FROM inserted
UNION ALL
SELECT sb.id as batch_id FROM signal_batches sb
JOIN isn ON sb.isn_id = isn.id
WHERE sb.account_id = $2 AND sb.is_latest = TRUE
  AND NOT EXISTS (SELECT 1 FROM inserted)
`

type CreateOrGetWebUserSignalBatchParams struct {
	Slug      string    `json:"slug"`
	AccountID uuid.UUID `json:"account_id"`
}

func (q *Queries) CreateOrGetWebUserSignalBatch(ctx context.Context, arg CreateOrGetWebUserSignalBatchParams) (uuid.UUID, error) {
	row := q.db.QueryRow(ctx, CreateOrGetWebUserSignalBatch, arg.Slug, arg.AccountID)
	var batch_id uuid.UUID
	err := row.Scan(&batch_id)
	return batch_id, err
}

const CreateSignalBatch = `-- name: CreateSignalBatch :one
INSERT INTO signal_batches (
    id,
    created_at,
    updated_at,
    isn_id,
    account_id,
    is_latest
) VALUES (
    gen_random_uuid(), 
    now(), 
    now(), 
    $1, 
    $2, 
    TRUE
)
RETURNING id, created_at, updated_at, isn_id, account_id, is_latest
`

type CreateSignalBatchParams struct {
	IsnID     uuid.UUID `json:"isn_id"`
	AccountID uuid.UUID `json:"account_id"`
}

func (q *Queries) CreateSignalBatch(ctx context.Context, arg CreateSignalBatchParams) (SignalBatch, error) {
	row := q.db.QueryRow(ctx, CreateSignalBatch, arg.IsnID, arg.AccountID)
	var i SignalBatch
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsnID,
		&i.AccountID,
		&i.IsLatest,
	)
	return i, err
}

const GetBatchesWithOptionalFilters = `-- name: GetBatchesWithOptionalFilters :many
WITH RankedBatches AS (
    SELECT
        sb.id as batch_id,
        sb.created_at,
        sb.updated_at,
        sb.account_id,
        sb.is_latest,
        i.slug as isn_slug,
        ROW_NUMBER() OVER (PARTITION BY sb.account_id ORDER BY sb.created_at DESC) as rn
    FROM signal_batches sb
    JOIN isn i ON i.id = sb.isn_id
    WHERE i.slug = $4
        -- Account permission: users see own batches, owner role sees all
        AND (sb.account_id = $5::uuid
             OR $2::boolean = true)
        AND ($6::timestamptz IS NULL OR sb.created_at >= $6::timestamptz)
        AND ($7::timestamptz IS NULL OR sb.created_at <= $7::timestamptz)
        -- Closed date filters (only apply to closed batches: is_latest = false)
        AND ($8::timestamptz IS NULL OR (sb.is_latest = false AND sb.updated_at >= $8::timestamptz))
        AND ($9::timestamptz IS NULL OR (sb.is_latest = false AND sb.updated_at <= $9::timestamptz))
)
SELECT
    batch_id,
    created_at,
    updated_at,
    account_id,
    is_latest,
    isn_slug
FROM RankedBatches
WHERE
    -- Apply latest & previous filtering only when latest=true
    ($1::boolean IS NOT TRUE
     OR ($1::boolean = true AND $2::boolean = true AND rn = 1)
     OR ($1::boolean = true AND $2::boolean = false AND is_latest = true))
    AND
    ($3::boolean IS NOT TRUE
     OR ($3::boolean = true AND $2::boolean = true AND rn = 2)
     OR ($3::boolean = true AND $2::boolean = false))
ORDER BY created_at DESC
LIMIT CASE
    WHEN $1::boolean = true AND $2::boolean = false THEN 1  
    WHEN $3::boolean = true AND $2::boolean = false THEN 1
    ELSE NULL  -- No limit for admin latest/previous (returns per account)
END
OFFSET CASE
    WHEN $3::boolean = true AND $2::boolean = false THEN 1  -- Skip the latest to get previous (members only)
    ELSE 0
END
`

type GetBatchesWithOptionalFiltersParams struct {
	Latest              *bool      `json:"latest"`
	IsAdmin             *bool      `json:"is_admin"`
	Previous            *bool      `json:"previous"`
	IsnSlug             string     `json:"isn_slug"`
	RequestingAccountID *uuid.UUID `json:"requesting_account_id"`
	CreatedAfter        *time.Time `json:"created_after"`
	CreatedBefore       *time.Time `json:"created_before"`
	ClosedAfter         *time.Time `json:"closed_after"`
	ClosedBefore        *time.Time `json:"closed_before"`
}

type GetBatchesWithOptionalFiltersRow struct {
	BatchID   uuid.UUID `json:"batch_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	AccountID uuid.UUID `json:"account_id"`
	IsLatest  bool      `json:"is_latest"`
	IsnSlug   string    `json:"isn_slug"`
}

func (q *Queries) GetBatchesWithOptionalFilters(ctx context.Context, arg GetBatchesWithOptionalFiltersParams) ([]GetBatchesWithOptionalFiltersRow, error) {
	rows, err := q.db.Query(ctx, GetBatchesWithOptionalFilters,
		arg.Latest,
		arg.IsAdmin,
		arg.Previous,
		arg.IsnSlug,
		arg.RequestingAccountID,
		arg.CreatedAfter,
		arg.CreatedBefore,
		arg.ClosedAfter,
		arg.ClosedBefore,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetBatchesWithOptionalFiltersRow
	for rows.Next() {
		var i GetBatchesWithOptionalFiltersRow
		if err := rows.Scan(
			&i.BatchID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccountID,
			&i.IsLatest,
			&i.IsnSlug,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetFailedSignalsByBatchID = `-- name: GetFailedSignalsByBatchID :many
SELECT DISTINCT
    sb.id as batch_id,
    sb.created_at as batch_opened_at,
    sb.account_id,
    i.slug as isn_slug,
    sb.is_latest,
    spf.signal_type_slug,
    spf.signal_type_sem_ver,
    spf.local_ref,
    spf.error_code,
    spf.error_message
FROM signal_batches sb
JOIN isn i ON i.id = sb.isn_id
JOIN signal_processing_failures spf ON spf.signal_batch_id = sb.id
JOIN signal_types st ON st.slug = spf.signal_type_slug
    AND st.sem_ver = spf.signal_type_sem_ver
WHERE sb.id = $1
AND NOT EXISTS (
        SELECT 1 FROM signals s 
        JOIN signal_versions sv ON sv.signal_id = s.id
        WHERE s.local_ref = spf.local_ref
            AND s.signal_type_id = st.id
            AND sv.created_at > spf.created_at
    )
`

type GetFailedSignalsByBatchIDRow struct {
	BatchID          uuid.UUID `json:"batch_id"`
	BatchOpenedAt    time.Time `json:"batch_opened_at"`
	AccountID        uuid.UUID `json:"account_id"`
	IsnSlug          string    `json:"isn_slug"`
	IsLatest         bool      `json:"is_latest"`
	SignalTypeSlug   string    `json:"signal_type_slug"`
	SignalTypeSemVer string    `json:"signal_type_sem_ver"`
	LocalRef         string    `json:"local_ref"`
	ErrorCode        string    `json:"error_code"`
	ErrorMessage     string    `json:"error_message"`
}

// failed local_refs that were not subsequently loaded
func (q *Queries) GetFailedSignalsByBatchID(ctx context.Context, id uuid.UUID) ([]GetFailedSignalsByBatchIDRow, error) {
	rows, err := q.db.Query(ctx, GetFailedSignalsByBatchID, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetFailedSignalsByBatchIDRow
	for rows.Next() {
		var i GetFailedSignalsByBatchIDRow
		if err := rows.Scan(
			&i.BatchID,
			&i.BatchOpenedAt,
			&i.AccountID,
			&i.IsnSlug,
			&i.IsLatest,
			&i.SignalTypeSlug,
			&i.SignalTypeSemVer,
			&i.LocalRef,
			&i.ErrorCode,
			&i.ErrorMessage,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetLatestBatchByAccountAndIsnSlug = `-- name: GetLatestBatchByAccountAndIsnSlug :one
SELECT sb.id, sb.created_at, sb.updated_at, sb.isn_id, sb.account_id, sb.is_latest, i.slug as isn_slug FROM signal_batches sb
JOIN isn i
ON i.id = sb.isn_id
WHERE sb.account_id = $1
AND i.slug = $2
AND sb.is_latest = TRUE
`

type GetLatestBatchByAccountAndIsnSlugParams struct {
	AccountID uuid.UUID `json:"account_id"`
	Slug      string    `json:"slug"`
}

type GetLatestBatchByAccountAndIsnSlugRow struct {
	ID        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsnID     uuid.UUID `json:"isn_id"`
	AccountID uuid.UUID `json:"account_id"`
	IsLatest  bool      `json:"is_latest"`
	IsnSlug   string    `json:"isn_slug"`
}

func (q *Queries) GetLatestBatchByAccountAndIsnSlug(ctx context.Context, arg GetLatestBatchByAccountAndIsnSlugParams) (GetLatestBatchByAccountAndIsnSlugRow, error) {
	row := q.db.QueryRow(ctx, GetLatestBatchByAccountAndIsnSlug, arg.AccountID, arg.Slug)
	var i GetLatestBatchByAccountAndIsnSlugRow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsnID,
		&i.AccountID,
		&i.IsLatest,
		&i.IsnSlug,
	)
	return i, err
}

const GetLatestIsnSignalBatchesByAccountID = `-- name: GetLatestIsnSignalBatchesByAccountID :many
SELECT sb.id, sb.created_at, sb.updated_at, sb.isn_id, sb.account_id, sb.is_latest, i.slug as isn_slug FROM signal_batches sb 
JOIN isn i
    ON sb.isn_id = i.id
WHERE account_id = $1
AND is_latest = TRUE
`

type GetLatestIsnSignalBatchesByAccountIDRow struct {
	ID        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsnID     uuid.UUID `json:"isn_id"`
	AccountID uuid.UUID `json:"account_id"`
	IsLatest  bool      `json:"is_latest"`
	IsnSlug   string    `json:"isn_slug"`
}

func (q *Queries) GetLatestIsnSignalBatchesByAccountID(ctx context.Context, accountID uuid.UUID) ([]GetLatestIsnSignalBatchesByAccountIDRow, error) {
	rows, err := q.db.Query(ctx, GetLatestIsnSignalBatchesByAccountID, accountID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetLatestIsnSignalBatchesByAccountIDRow
	for rows.Next() {
		var i GetLatestIsnSignalBatchesByAccountIDRow
		if err := rows.Scan(
			&i.ID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsnID,
			&i.AccountID,
			&i.IsLatest,
			&i.IsnSlug,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetLatestSignalBatchByIsnSlugAndBatchID = `-- name: GetLatestSignalBatchByIsnSlugAndBatchID :one
SELECT sb.id, sb.created_at, sb.updated_at, sb.isn_id, sb.account_id, sb.is_latest, i.slug as isn_slug FROM signal_batches sb
JOIN isn i
ON i.id = sb.isn_id
WHERE i.slug = $1
AND sb.id = $2
AND sb.is_latest = TRUE
`

type GetLatestSignalBatchByIsnSlugAndBatchIDParams struct {
	Slug string    `json:"slug"`
	ID   uuid.UUID `json:"id"`
}

type GetLatestSignalBatchByIsnSlugAndBatchIDRow struct {
	ID        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsnID     uuid.UUID `json:"isn_id"`
	AccountID uuid.UUID `json:"account_id"`
	IsLatest  bool      `json:"is_latest"`
	IsnSlug   string    `json:"isn_slug"`
}

func (q *Queries) GetLatestSignalBatchByIsnSlugAndBatchID(ctx context.Context, arg GetLatestSignalBatchByIsnSlugAndBatchIDParams) (GetLatestSignalBatchByIsnSlugAndBatchIDRow, error) {
	row := q.db.QueryRow(ctx, GetLatestSignalBatchByIsnSlugAndBatchID, arg.Slug, arg.ID)
	var i GetLatestSignalBatchByIsnSlugAndBatchIDRow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsnID,
		&i.AccountID,
		&i.IsLatest,
		&i.IsnSlug,
	)
	return i, err
}

const GetLoadedSignalsSummaryByBatchID = `-- name: GetLoadedSignalsSummaryByBatchID :many
SELECT
    COUNT(*) as submitted_count,
    st.slug AS signal_type_slug,
    st.sem_ver AS signal_type_sem_ver
FROM
    signal_batches sb
JOIN
    signal_versions sv ON sv.signal_batch_id = sb.id
JOIN
    latest_signal_versions lsv ON lsv.signal_id = sv.signal_id AND lsv.id = sv.id
JOIN
    signals s ON s.id = lsv.signal_id
JOIN
    signal_types st on st.id = s.signal_type_id
WHERE
    sb.id = $1
    AND NOT EXISTS ( -- do not count signals that failed processing and have not been corrected yet
        SELECT 1 FROM signal_processing_failures spf
        WHERE spf.signal_batch_id = $1
            AND spf.local_ref = s.local_ref
            AND spf.signal_type_slug = st.slug
            AND spf.signal_type_sem_ver = st.sem_ver
            AND spf.created_at > lsv.created_at
        )
GROUP BY st.slug, st.sem_ver
`

type GetLoadedSignalsSummaryByBatchIDRow struct {
	SubmittedCount   int64  `json:"submitted_count"`
	SignalTypeSlug   string `json:"signal_type_slug"`
	SignalTypeSemVer string `json:"signal_type_sem_ver"`
}

// count of sucessfully loaded signals grouped by signal type (note that a local_ref can be submitted multiple times and only the latest version is counted).
//
// where a signal has failed processing and has not subsequently been loaded again, it is not counted
// Uses latest_signal_versions view to avoid ROW_NUMBER() window function
func (q *Queries) GetLoadedSignalsSummaryByBatchID(ctx context.Context, id uuid.UUID) ([]GetLoadedSignalsSummaryByBatchIDRow, error) {
	rows, err := q.db.Query(ctx, GetLoadedSignalsSummaryByBatchID, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetLoadedSignalsSummaryByBatchIDRow
	for rows.Next() {
		var i GetLoadedSignalsSummaryByBatchIDRow
		if err := rows.Scan(&i.SubmittedCount, &i.SignalTypeSlug, &i.SignalTypeSemVer); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetSignalBatchByID = `-- name: GetSignalBatchByID :one
SELECT sb.id, sb.created_at, sb.updated_at, sb.isn_id, sb.account_id, sb.is_latest, i.slug as isn_slug FROM signal_batches sb
JOIN isn i 
ON i.id = sb.isn_id
WHERE sb.id = $1
`

type GetSignalBatchByIDRow struct {
	ID        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsnID     uuid.UUID `json:"isn_id"`
	AccountID uuid.UUID `json:"account_id"`
	IsLatest  bool      `json:"is_latest"`
	IsnSlug   string    `json:"isn_slug"`
}

func (q *Queries) GetSignalBatchByID(ctx context.Context, id uuid.UUID) (GetSignalBatchByIDRow, error) {
	row := q.db.QueryRow(ctx, GetSignalBatchByID, id)
	var i GetSignalBatchByIDRow
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsnID,
		&i.AccountID,
		&i.IsLatest,
		&i.IsnSlug,
	)
	return i, err
}

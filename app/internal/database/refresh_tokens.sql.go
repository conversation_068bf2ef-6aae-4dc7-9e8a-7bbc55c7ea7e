// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: refresh_tokens.sql

package database

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const GetRefreshToken = `-- name: GetRefreshToken :one
SELECT user_account_id, expires_at, revoked_at FROM refresh_tokens where hashed_token = $1
`

type GetRefreshTokenRow struct {
	UserAccountID uuid.UUID  `json:"user_account_id"`
	ExpiresAt     time.Time  `json:"expires_at"`
	RevokedAt     *time.Time `json:"revoked_at"`
}

func (q *Queries) GetRefreshToken(ctx context.Context, hashedToken string) (GetRefreshTokenRow, error) {
	row := q.db.QueryRow(ctx, GetRefreshToken, hashedToken)
	var i GetRefreshTokenRow
	err := row.Scan(&i.UserAccountID, &i.ExpiresAt, &i.RevokedAt)
	return i, err
}

const GetValidRefreshTokenByUserAccountId = `-- name: GetValidRefreshTokenByUserAccountId :one
SELECT hashed_token, expires_at
FROM refresh_tokens
WHERE user_account_id = $1
  AND revoked_at IS NULL
  AND expires_at > NOW()
`

type GetValidRefreshTokenByUserAccountIdRow struct {
	HashedToken string    `json:"hashed_token"`
	ExpiresAt   time.Time `json:"expires_at"`
}

func (q *Queries) GetValidRefreshTokenByUserAccountId(ctx context.Context, userAccountID uuid.UUID) (GetValidRefreshTokenByUserAccountIdRow, error) {
	row := q.db.QueryRow(ctx, GetValidRefreshTokenByUserAccountId, userAccountID)
	var i GetValidRefreshTokenByUserAccountIdRow
	err := row.Scan(&i.HashedToken, &i.ExpiresAt)
	return i, err
}

const InsertRefreshToken = `-- name: InsertRefreshToken :one
INSERT INTO refresh_tokens (hashed_token, user_account_id, created_at, updated_at, expires_at)
VALUES ( $1,$2, NOW(), NOW(), $3)
RETURNING hashed_token, user_account_id
`

type InsertRefreshTokenParams struct {
	HashedToken   string    `json:"hashed_token"`
	UserAccountID uuid.UUID `json:"user_account_id"`
	ExpiresAt     time.Time `json:"expires_at"`
}

type InsertRefreshTokenRow struct {
	HashedToken   string    `json:"hashed_token"`
	UserAccountID uuid.UUID `json:"user_account_id"`
}

func (q *Queries) InsertRefreshToken(ctx context.Context, arg InsertRefreshTokenParams) (InsertRefreshTokenRow, error) {
	row := q.db.QueryRow(ctx, InsertRefreshToken, arg.HashedToken, arg.UserAccountID, arg.ExpiresAt)
	var i InsertRefreshTokenRow
	err := row.Scan(&i.HashedToken, &i.UserAccountID)
	return i, err
}

const RevokeAllRefreshTokensForUser = `-- name: RevokeAllRefreshTokensForUser :execrows
UPDATE refresh_tokens SET (updated_at, revoked_at) = (NOW(), NOW()) 
WHERE user_account_id = $1
AND revoked_at IS NULL
`

func (q *Queries) RevokeAllRefreshTokensForUser(ctx context.Context, userAccountID uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, RevokeAllRefreshTokensForUser, userAccountID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const RevokeRefreshToken = `-- name: RevokeRefreshToken :execrows
UPDATE refresh_tokens SET (updated_at, revoked_at) = (NOW(), NOW()) 
WHERE hashed_token = $1
`

func (q *Queries) RevokeRefreshToken(ctx context.Context, hashedToken string) (int64, error) {
	result, err := q.db.Exec(ctx, RevokeRefreshToken, hashedToken)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}
